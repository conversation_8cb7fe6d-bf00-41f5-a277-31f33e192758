# Site Web Pôle Agriculture - Cité des Métiers et des Compétences

## Description
Site web professionnel pour le Pôle Agriculture de la Cité des Métiers et des Compétences, présentant les formations, actualités et informations sur l'établissement.

## Structure du site

### Pages principales
- **index.php** - Page d'accueil avec présentation générale
- **presentation.php** - Présentation détaillée du pôle (histoire, valeurs, équipe, partenaires)
- **formations.php** - Catalogue des formations disponibles avec filtres
- **actualites.php** - Actualités et événements avec système de filtrage

### Fichiers de style
- **style.css** - Feuille de style principale avec design responsive

### Dossier images
- **images/** - Dossier contenant toutes les images du site

## Fonctionnalités

### Design
- Design moderne et professionnel
- Couleurs principales : vert (#4a7c59) et blanc
- Responsive design pour tous les appareils
- Animations et effets de survol

### Navigation
- Menu de navigation fixe en haut
- Navigation cohérente entre toutes les pages
- Liens actifs selon la page courante

### Page d'accueil
- Section hero avec présentation principale
- Statistiques du pôle
- Section agriculture avec image
- Actualités récentes
- Call-to-action pour les inscriptions

### Page de présentation
- Histoire et mission du pôle
- 6 valeurs fondamentales avec icônes
- Projets en cours avec barres de progression
- Équipe de 4 membres avec photos
- Partenaires principaux
- Témoignages d'anciens étudiants

### Page formations
- 6 formations disponibles avec images
- Système de filtres par catégorie (Production, Élevage, Gestion)
- Informations détaillées sur chaque formation
- Section informations générales (admission, débouchés, etc.)
- Call-to-action pour contact

### Page actualités
- Actualités récentes avec système de filtres
- Section "À la une" mise en avant
- Projets récents des étudiants
- Newsletter avec formulaire d'inscription

## Installation

1. Placez tous les fichiers dans votre serveur web (XAMPP, WAMP, etc.)
2. Assurez-vous que PHP est activé
3. Ajoutez les images dans le dossier `images/`
4. Accédez au site via votre navigateur

## Images nécessaires

Pour que le site fonctionne parfaitement, ajoutez ces images dans le dossier `images/` :

### Images générales
- agriculture.jpg (section agriculture page d'accueil)
- presentation.jpg (page présentation)
- featured-news.jpg (actualités à la une)

### Images actualités
- news1.jpg, news2.jpg, news3.jpg (actualités page d'accueil)
- news-main1.jpg, news-main2.jpg (actualités principales)
- news-side1.jpg, news-side2.jpg, news-side3.jpg (actualités secondaires)

### Images formations
- formation1.jpg à formation6.jpg (6 formations)

### Images équipe
- team1.jpg à team4.jpg (4 membres de l'équipe)

### Images partenaires
- partner1.png à partner4.png (logos partenaires)

### Images témoignages
- testimonial1.jpg, testimonial2.jpg (photos témoignages)

### Images projets
- project1.jpg à project3.jpg (projets récents)

## Technologies utilisées
- HTML5
- CSS3 avec Flexbox et Grid
- JavaScript (filtres et interactions)
- PHP (structure des pages)
- Font Awesome (icônes)

## Fonctionnalités JavaScript
- Filtres pour les formations par catégorie
- Filtres pour les actualités par type
- Formulaire newsletter avec validation
- Effets d'animation au survol

## Responsive Design
Le site est entièrement responsive et s'adapte à :
- Desktop (1200px+)
- Tablette (768px - 1199px)
- Mobile (< 768px)

## Personnalisation
Pour personnaliser le site :
1. Modifiez les couleurs dans le fichier CSS (variables CSS recommandées)
2. Remplacez les images par vos propres visuels
3. Adaptez les textes selon vos besoins
4. Ajoutez/supprimez des formations ou actualités

## Support
Le site est compatible avec tous les navigateurs modernes :
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Notes importantes
- Assurez-vous d'avoir un serveur web avec PHP pour tester localement
- Les images sont essentielles pour l'apparence du site
- Le site utilise Font Awesome via CDN (connexion internet requise)
- Tous les formulaires sont fonctionnels côté frontend

## Structure des fichiers
```
projet syn/
├── index.php
├── presentation.php
├── formations.php
├── actualites.php
├── style.css
├── README.md
└── images/
    ├── agriculture.jpg
    ├── formation1.jpg
    ├── team1.jpg
    └── ... (autres images)
```
