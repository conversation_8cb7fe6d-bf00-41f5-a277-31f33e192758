/* Reset et styles de base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4a7c59;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav a:hover,
.nav a.active {
    color: #4a7c59;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #4a7c59 0%, #6b9b7a 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 70px;
}

.hero-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-bottom: 3rem;
}

.hero-text h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.hero-text h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-text p {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #fff;
    color: #4a7c59;
}

.btn-primary:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #4a7c59;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Hero Card */
.hero-card .card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    color: #333;
}

.hero-card h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    color: #ffd700;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    text-align: center;
}

.stat .number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat .label {
    opacity: 0.9;
}

/* About Section */
.about {
    padding: 80px 0;
    background: #f8f9fa;
}

.about h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    margin-bottom: 2rem;
    text-align: center;
}

.about p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.about .btn {
    display: block;
    width: fit-content;
    margin: 2rem auto 0;
}

/* Agriculture Section */
.agriculture-section {
    padding: 80px 0;
}

.agriculture-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.agriculture-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.agriculture-text h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    margin-bottom: 1.5rem;
}

.agriculture-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

/* Statistics Section */
.statistics {
    background: #4a7c59;
    color: white;
    padding: 80px 0;
    text-align: center;
}

.statistics h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.statistics > p {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* News Section */
.news {
    padding: 80px 0;
    background: #f8f9fa;
}

.news h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 3rem;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.news-item {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.news-item:hover {
    transform: translateY(-5px);
}

.news-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.news-item h3 {
    padding: 1.5rem 1.5rem 1rem;
    color: #4a7c59;
    font-size: 1.2rem;
}

.news-item p {
    padding: 0 1.5rem;
    color: #666;
    line-height: 1.6;
}

.news-item .date {
    display: block;
    padding: 1rem 1.5rem 1.5rem;
    color: #999;
    font-size: 0.9rem;
}

/* CTA Section */
.cta {
    background: #4a7c59;
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta .btn-primary {
    background: white;
    color: #4a7c59;
    font-size: 1.1rem;
    padding: 15px 30px;
}

/* Footer */
.footer {
    background: #2d4a35;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    color: #6b9b7a;
}

.footer-section p {
    line-height: 1.8;
    opacity: 0.9;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: white;
    text-decoration: none;
    opacity: 0.9;
    transition: opacity 0.3s;
}

.footer-section ul li a:hover {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #4a7c59;
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    transition: background 0.3s;
}

.social-links a:hover {
    background: #6b9b7a;
}

.footer-bottom {
    border-top: 1px solid #4a7c59;
    padding-top: 2rem;
    text-align: center;
    opacity: 0.8;
}

/* Page Hero */
.page-hero {
    background: linear-gradient(135deg, #4a7c59 0%, #6b9b7a 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 70px;
    text-align: center;
}

.page-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.page-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Histoire et Mission */
.history-mission {
    padding: 80px 0;
    background: #f8f9fa;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.text-content h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    margin-bottom: 2rem;
}

.text-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #555;
}

.image-content img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Values Section */
.values {
    padding: 80px 0;
}

.values h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.values > p {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: #666;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.value-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.value-item:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: #4a7c59;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.value-item h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.value-item p {
    color: #666;
    line-height: 1.6;
}

/* Projects Section */
.projects {
    padding: 80px 0;
    background: #f8f9fa;
}

.projects h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.projects > p {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: #666;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.project-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.project-item h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.project-item p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress {
    height: 100%;
    background: #4a7c59;
    transition: width 0.3s;
}

.progress-text {
    font-weight: bold;
    color: #4a7c59;
}

/* Team Section */
.team {
    padding: 80px 0;
}

.team h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.team > p {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: #666;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.team-member {
    text-align: center;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.team-member:hover {
    transform: translateY(-5px);
}

.team-member img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.5rem;
}

.team-member h3 {
    color: #4a7c59;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.team-member .role {
    color: #666;
    font-weight: bold;
    margin-bottom: 1rem;
}

.team-member p {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
}

/* Partners Section */
.partners {
    padding: 80px 0;
    background: #f8f9fa;
}

.partners h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.partners > p {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: #666;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.partner-item {
    text-align: center;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.partner-item:hover {
    transform: translateY(-5px);
}

.partner-item img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 1.5rem;
}

.partner-item h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.partner-item p {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
}

.testimonials h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.testimonials > p {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: #666;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.testimonial-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.testimonial-content {
    margin-bottom: 2rem;
}

.testimonial-content p {
    font-style: italic;
    color: #555;
    line-height: 1.8;
    font-size: 1.1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author h4 {
    color: #4a7c59;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #666;
    font-size: 0.9rem;
}

/* Formations Page Styles */
.formations-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
}

.filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #4a7c59;
    background: transparent;
    color: #4a7c59;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: #4a7c59;
    color: white;
}

.formations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.formation-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.formation-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.formation-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.formation-content {
    padding: 1.5rem;
}

.formation-level {
    display: inline-block;
    background: #4a7c59;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.formation-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    line-height: 1.4;
}

.formation-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.formation-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #666;
}

.formation-details span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.formation-details i {
    color: #4a7c59;
}

.formation-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4a7c59;
}

.formation-info {
    padding: 80px 0;
}

.formation-info h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.formation-info > p {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.info-section {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.info-section h3 {
    color: #4a7c59;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.info-section ul {
    list-style: none;
}

.info-section ul li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 1.5rem;
}

.info-section ul li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4a7c59;
    font-weight: bold;
}

.info-section ul li:last-child {
    border-bottom: none;
}

.additional-info {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
}

.additional-info h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
    margin-top: 2rem;
}

.additional-info h3:first-child {
    margin-top: 0;
}

.additional-info p {
    color: #666;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.cta-formation {
    background: #4a7c59;
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta-formation h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-formation p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 15px 25px;
    font-size: 1rem;
}

.cta-buttons .btn-secondary {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.cta-buttons .btn-secondary:hover {
    background: white;
    color: #4a7c59;
}

/* Actualités Page Styles */
.latest-news {
    padding: 80px 0;
    background: #f8f9fa;
}

.news-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.news-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.main-news {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.side-news {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.news-article {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.news-article:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.news-article.large {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
}

.news-article.small {
    display: block;
}

.news-article img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.news-article.small img {
    height: 150px;
}

.article-content {
    padding: 2rem;
}

.news-article.small .article-content {
    padding: 1.5rem;
}

.article-category {
    display: inline-block;
    background: #4a7c59;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.article-date {
    color: #666;
    font-size: 0.9rem;
    margin-left: 1rem;
}

.news-article h3 {
    color: #333;
    margin: 1rem 0;
    font-size: 1.4rem;
    line-height: 1.4;
}

.news-article.small h3 {
    font-size: 1.1rem;
}

.news-article p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.read-more {
    color: #4a7c59;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.read-more:hover {
    color: #6b9b7a;
}

/* Featured News */
.featured-news {
    padding: 80px 0;
}

.featured-news h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.featured-news > p {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.featured-article {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.featured-content h3 {
    font-size: 2rem;
    color: #4a7c59;
    margin-bottom: 1.5rem;
}

.featured-content p {
    color: #666;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.featured-image img {
    width: 100%;
    border-radius: 10px;
}

/* Recent Projects */
.recent-projects {
    padding: 80px 0;
    background: #f8f9fa;
}

.recent-projects h2 {
    font-size: 2.5rem;
    color: #4a7c59;
    text-align: center;
    margin-bottom: 1rem;
}

.recent-projects > p {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.project-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.project-card:hover {
    transform: translateY(-5px);
}

.project-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.project-content {
    padding: 1.5rem;
}

.project-content h3 {
    color: #4a7c59;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.project-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.project-details span {
    color: #666;
    font-size: 0.9rem;
}

/* Newsletter */
.newsletter {
    background: #4a7c59;
    color: white;
    padding: 80px 0;
    text-align: center;
}

.newsletter h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.newsletter p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    display: flex;
    justify-content: center;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
}

.newsletter-form button {
    padding: 15px 25px;
    font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .agriculture-content {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }

    .nav {
        flex-direction: column;
        gap: 1rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .formations-grid {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .news-container {
        grid-template-columns: 1fr;
    }

    .news-article.large {
        grid-template-columns: 1fr;
    }

    .featured-article {
        grid-template-columns: 1fr;
        padding: 2rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }
}
